/* tabs start */
.home-tab-container {
  @apply flex w-fit gap-custom-xs bg-custom-bg-secondary p-custom-xs rounded-custom-md;
}
.home-tab {
  @apply h-[2.1875rem] text-custom-md font-semibold flex items-center gap-custom-sm px-custom-md py-custom-sm cursor-pointer transition-transform duration-150 ease-in-out;
}
.active-tab {
  @apply rounded-custom-md border border-custom-border bg-white text-semantic-gray-900 shadow-sm;
}
.inactive-tab {
  @apply bg-custom-bg-secondary rounded-custom-md text-custom-text-secondary;
}
.inactive-tab:hover {
  @apply bg-custom-border;
}
.home-tab-icon {
  @apply text-custom-lg;
}
/* tabs end */
/* Header start */
.home-header-container {
  @apply flex flex-col justify-center items-start h-1/6 w-full overflow-hidden;
  background: linear-gradient(to right, hsl(var(--muted)) 70%, hsl(20 25% 8% / 0.02) 80%, hsl(20 25% 8% / 0.01));
}
.greetings-text {
  @apply text-custom-2xl font-medium leading-custom-loose;
}
.info-title {
  @apply text-custom-md leading-custom-relaxed font-medium text-custom-text-secondary;
}
.info-text {
  @apply mt-custom-xs text-custom-md font-medium leading-custom-relaxed text-custom-text-primary;
}
.task-badge {
  @apply text-custom-sm font-semibold leading-custom-tight rounded-custom-xl;
  background-color: hsl(20 75% 50% / 0.1);
  color: hsl(20 75% 50%);
  padding: 0.25rem 0.75rem;
}
/* Header end */
/* Content start */
.content-loading-overlay {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
}
/* Content end */
/* ClientSideComponent start */
.client-side-component-sticky {
  @apply sticky top-0 z-10 bg-custom-bg-primary;
}
/* ClientSideComponent end */
/* Table start */
.table-container {
  @apply rounded-custom-lg overflow-hidden border border-custom-border-input mt-custom-xl;
}
table {
  @apply w-full border-separate border-spacing-0;
}
.assigned-tasks-table {
  @apply min-w-full bg-custom-bg-primary overflow-x-auto;
}
.assigned-tasks-table-head-container {
  @apply bg-custom-bg-card text-custom-text-muted text-custom-md font-semibold;
}
.assigned-tasks-table-head {
  @apply py-custom-xs px-custom-sm border-b text-left;
}
.assigned-tasks-table-data {
  @apply py-custom-sm px-custom-md border-b text-custom-md font-normal text-custom-text-primary;
}
.assigned-tasks-priority {
  @apply text-custom-sm p-[0.0625rem_0.5rem] gap-[0.375rem] flex items-center w-fit font-medium rounded-custom-sm;
}
/* Utility Classes */
.table-tooltip-trigger {
  @apply relative cursor-pointer;
}
/* Priority Styles */
.low-priority {
  @apply bg-gray-100 text-gray-700;
}
.medium-priority {
  @apply bg-warning-100 text-warning-700;
}
.high-priority {
  @apply bg-primary-100 text-primary-700;
}
.critical-priority {
  @apply bg-destructive-100 text-destructive-700;
}
/* Table end */
/* ProjectList styles start */
.project-card {
  @apply flex w-full cursor-pointer items-center justify-between p-custom-lg border border-custom-border-card rounded-custom-lg bg-custom-bg-primary sm:w-1/3;
}
.project-card:hover {
  @apply shadow-custom-card;
}
.project-card-content {
  @apply flex items-center gap-custom-md;
}
.project-card-logo {
  @apply h-10 w-10 rounded-custom-lg;
}
.project-card-logo.local-image {
  @apply rounded-custom-lg;
}
.project-card-info {
  @apply flex flex-col;
}
.project-card-name {
  @apply mt-custom-xs font-semibold group-hover:underline group-hover:text-custom-primary-hover leading-custom-normal;
}
.project-card-date {
  @apply text-custom-text-secondary text-custom-sm leading-custom-normal;
}
.project-card-route {
  @apply hidden group-hover:block;
}
.project-card-route-icon {
  @apply w-5 h-5 rounded-custom-lg;
}
.projects-list {
  @apply mt-[1.5rem] flex w-full gap-custom-lg;
}
.projects-list-empty {
  @apply w-full mt-custom-md;
}
.projects-list-empty-text {
  @apply text-center mt-custom-md text-custom-text-secondary text-custom-lg;
}
/* ProjectList styles end */
/* RecentActivityList styles start */
.recent-activity-title {
  @apply font-semibold text-custom-lg ml-custom-xs mt-[1.5rem];
}
.recent-activity-list {
  @apply w-full mt-custom-sm;
}
.activity-card {
  @apply flex cursor-pointer items-center justify-between bg-white p-[1rem_0.75rem_1rem_1rem] border border-custom-border-input;
}
.activity-card-content {
  @apply flex flex-col;
}
.activity-card-title {
  @apply font-semibold text-custom-md;
}
.activity-card-date {
  @apply text-custom-text-tertiary text-custom-base;
}
.activity-card-chevron {
  @apply text-custom-text-secondary;
}
.activity-card-first {
  @apply rounded-t-custom-lg;
}
.activity-card-last {
  @apply rounded-b-custom-lg;
}
.activity-card-middle {
  @apply rounded-none;
}
/* RecentActivityList styles end */
/* Discussion list styles start */
.my-discussion-card {
  @apply w-full cursor-pointer border border-custom-border-input bg-white p-[1rem_0.75rem_1rem_1rem] transition-shadow duration-200 ease-in-out;
}
.my-discussion-card-username {
  @apply font-semibold text-custom-md;
}
.my-discussion-card-description {
  @apply text-custom-base text-custom-text-tertiary;
}
/* Loading styles */
.loading-container {
  @apply flex items-center justify-center h-screen;
}
.loading-spinner {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
}
/* Discussion list container */
.discussion-list-container {
  @apply flex flex-col items-center w-full mt-[1.5rem];
}
/* Discussion card content */
.discussion-card-content {
  @apply flex justify-between text-custom-base text-custom-text-tertiary font-medium;
}
.discussion-card-left {
  @apply flex items-center mt-custom-xs;
}
.discussion-card-info {
  @apply flex flex-col cursor-pointer;
}
/* User profile image */
.user-profile-image {
  @apply w-10 h-10 mr-custom-md rounded-full;
}
/* Card border radius classes */
.card-rounded-top {
  @apply rounded-t-custom-lg;
}
.card-rounded-bottom {
  @apply rounded-b-custom-lg;
}
.card-rounded-none {
  @apply rounded-none;
}
/* Discussion list styles end */
/* Home page topbar ( get started ) -- START */
.tab-effect {
  @apply text-custom-sm font-medium text-foreground self-start bg-custom-bg-muted px-custom-lg py-custom-xs border-t-[0.1875rem] border-custom-primary shadow-custom-tab;
}
.tab-effect.dark-theme {
  background-color: hsl(25 20% 12% / 0.88) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  color: hsl(30 15% 92%) !important;
  border-top-color: hsl(15 90% 50%) !important;
  border-top: 3px solid hsl(15 90% 50%) !important;
  border-radius: 0 !important;
  margin-right: 1px !important;
  padding: 0.5rem 1rem !important;
  box-shadow: 0 1px 12px hsl(20 25% 8% / 0.4) !important;
}
/* Home page topbar ( get started ) -- END */
/* Common Utility */
.custom-text-ellipsis {
  @apply overflow-hidden text-ellipsis transition-all ease-in-out;
  display: -webkit-box;                /* For multiline ellipsis */
  -webkit-line-clamp: 2;               /* Limits to 2 lines */
  -webkit-box-orient: vertical;
}

/* Home page no-scroll styles - Prevents scrolling only on the home page */
.home-page-no-scroll {
  @apply h-screen overflow-hidden;
}

.home-content-wrapper {
  @apply h-full overflow-hidden;
}

/* Ensure the main content area fits within viewport */
.home-page-no-scroll .children-wrapper {
  @apply max-h-screen;
}

