import React, { useEffect, useState, useContext } from "react";
import { TopBarContext } from "@/components/Context/TopBarContext";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import Tabs from "@/components/Home/Tabs";
import Content from "@/components/Home/Content";
import { TABS, DEFAULT_TAB } from "@/constants/home/<USER>";
import { updateSearchParams } from "@/utils/helpers";

function ClientSideComponent() {
  const { addTab } = useContext(TopBarContext);
  const [activeTab, setActiveTab] = useState(DEFAULT_TAB);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const queryTab = searchParams.get("tab");

  useEffect(() => {
    if (!queryTab) {
      updateSearchParams(router, pathname, searchParams, "tab", DEFAULT_TAB);
    } else {
      setActiveTab(queryTab);
    }
  }, [queryTab, router, pathname, searchParams]);

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
    updateSearchParams(router, pathname, searchParams, "tab", tabId);
  };

  useEffect(() => {
    const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);
    const path = pathname.split("/")[1];
    document.title = capitalize(path);
  }, [pathname]);

  const activeTabLabel = TABS.find((tab) => tab.key === activeTab)?.label || "";

  return (
    <>
      <div className="client-side-component-sticky">
        <Tabs tabs={TABS} activeTab={activeTab} setActiveTab={handleTabClick} />
      </div>
      <Content activeTab={activeTabLabel} />
    </>
  );
}

export default ClientSideComponent;
