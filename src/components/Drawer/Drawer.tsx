import classNames from 'classnames'
import Modal from 'react-modal'
import CloseButton from '../CloseButton'
import { motion } from 'framer-motion'
import type ReactModal from 'react-modal'
import type { MouseEvent, ReactNode } from 'react'

export interface DrawerProps extends ReactModal.Props {
    bodyClass?: string
    closable?: boolean
    footer?: string | ReactNode
    footerClass?: string
    headerClass?: string
    height?: string | number
    lockScroll?: boolean
    onClose?: (e: MouseEvent<HTMLSpanElement> | MouseEvent<HTMLDivElement>) => void
    placement?: 'top' | 'right' | 'bottom' | 'left'
    showBackdrop?: boolean
    title?: string | ReactNode
    width?: string | number
    sidebarWidth?: string | number // New prop for sidebar width
    theme?: 'light' | 'dark'
}

const Drawer = (props: DrawerProps) => {
    const {
        bodyOpenClassName,
        bodyClass,
        children,
        className,
        closable = true,
        closeTimeoutMS = 300,
        footer,
        footerClass,
        headerClass,
        height = 400,
        isOpen,
        lockScroll = true,
        onClose,
        overlayClassName,
        placement = 'right',
        portalClassName,
        showBackdrop = true,
        title,
        width = 360,
        sidebarWidth = '5rem', // Default sidebar width
        theme = 'light',
        ...rest
    } = props

    // Theme classes
    const themeClasses = {
        light: {
            container: "bg-white border-gray-200 shadow-xl",
            overlay: "bg-black bg-opacity-50",
            header: "bg-white border-b border-gray-200 text-gray-900",
            headerTitle: "text-gray-900",
            headerIcon: "text-gray-600",
            body: "bg-white text-gray-900",
            footer: "bg-gray-50 border-t border-gray-200 text-gray-900",
            closeButton: "text-gray-500 hover:text-gray-700",
            gradientBlob: "bg-primary-500/20"
        },
        dark: {
            container: "bg-[#231f20] border-gray-700 shadow-2xl",
            overlay: "bg-black bg-opacity-70",
            header: "bg-[#231f20] border-b border-gray-700 text-gray-100",
            headerTitle: "text-gray-100",
            headerIcon: "text-gray-400",
            body: "bg-[#231f20] text-gray-100",
            footer: "bg-gray-800 border-t border-gray-700 text-gray-100",
            closeButton: "text-gray-400 hover:text-gray-200",
            gradientBlob: "bg-[#f26a1b]/30"
        }
    };

    const onCloseClick = (e: MouseEvent<HTMLSpanElement> | MouseEvent<HTMLDivElement>) => {
        onClose?.(e)
    }

    const renderCloseButton = <CloseButton onClick={onCloseClick} className={`typography-body-lg ${themeClasses[theme].closeButton}`} />

    const getStyle = (): {
        dimensionClass?: string
        contentStyle?: {
            width?: string | number
            height?: string | number
        }
        motionStyle: {
            [x: string]: string | number
        }
    } => {
        if (placement === 'left' || placement === 'right') {
            const sidebarOffset = typeof sidebarWidth === 'number' ? `${sidebarWidth}px` : sidebarWidth
            return {
                dimensionClass: 'vertical',
                contentStyle: { width },
                motionStyle: {
                    [placement]: isOpen ? sidebarWidth : `-${width}${typeof width === 'number' ? 'px' : ''}`,
                    transition: 'all 0.3s ease-in-out'
                },
            }
        }

        if (placement === 'top' || placement === 'bottom') {
            return {
                dimensionClass: 'horizontal',
                contentStyle: { height },
                motionStyle: {
                    [placement]: `-${height}${typeof height === 'number' ? 'px' : ''
                        }`,
                },
            }
        }

        return {
            motionStyle: {},
        }
    }

    const { dimensionClass, contentStyle, motionStyle } = getStyle()

    return (
        <Modal
            className={{
                base: classNames('drawer', className as string, themeClasses[theme].container),
                afterOpen: 'drawer-after-open',
                beforeClose: 'drawer-before-close',
            }}
            overlayClassName={{
                base: classNames(
                    'drawer-overlay',
                    overlayClassName as string,
                    !showBackdrop && 'bg-transparent-20',
                    showBackdrop && themeClasses[theme].overlay
                ),
                afterOpen: 'drawer-overlay-after-open',
                beforeClose: 'drawer-overlay-before-close',
            }}
            portalClassName={classNames('drawer-portal', portalClassName)}
            bodyOpenClassName={classNames(
                'drawer-open',
                lockScroll && 'main-content-area',
                bodyOpenClassName
            )}
            ariaHideApp={false}
            isOpen={isOpen}
            closeTimeoutMS={closeTimeoutMS}
            onRequestClose={onCloseClick} // Close drawer when clicking outside
            {...rest}
        >
            <motion.div
                className={classNames('drawer-content', dimensionClass, themeClasses[theme].container)}
                style={contentStyle}
                initial={motionStyle}
                animate={{
                    [placement as 'top' | 'right' | 'bottom' | 'left']: isOpen
                        ? 0
                        : motionStyle[placement],
                }}

            >
                {/* Orange gradient blob - themed */}
                <div className={`w-[446px] h-[302px] left-[99px] top-[-256px] absolute ${themeClasses[theme].gradientBlob} rounded-full blur-[120px]`} />

                {title || closable ? (
                  <div className={classNames('drawer-header relative z-10 border-b-0 bg-transparent py-4 px-4 flex items-center justify-between', themeClasses[theme].header, headerClass)}>
                        {typeof title === 'string' ? (
                            <div className="flex items-center">
                                <svg className={`w-5 h-5 mr-2 ${themeClasses[theme].headerIcon}`} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M3 9H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M9 21V9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                                <h4 className={`typography-heading-4 font-weight-medium ${themeClasses[theme].headerTitle}`}>{title}</h4>
                            </div>
                        ) : (
                            <span className={themeClasses[theme].headerTitle}>{title}</span>
                        )}
                        {closable && renderCloseButton}
                    </div>
                ) : null}
              <div className={classNames('drawer-body relative z-10', themeClasses[theme].body, bodyClass)}>
                    {children}
                </div>
                {footer && (
 <div className={classNames('drawer-footer relative z-10', themeClasses[theme].footer, footerClass)}>
                        {footer}
                    </div>
                )}
            </motion.div>
        </Modal>
    )
}

Drawer.displayName = 'Drawer'

export default Drawer