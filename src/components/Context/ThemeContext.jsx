"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

// Create ThemeContext
export const ThemeContext = createContext({
  isDarkMode: true,
  toggleDarkMode: () => {}
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  // Use localStorage to persist theme preference, default to light mode for all pages except home
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('isDarkMode');
      return saved !== null ? JSON.parse(saved) : false; // Default to light mode
    }
    return false; // Default to light mode for SSR
  });

  // Function to toggle dark mode
  const toggleDarkMode = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('isDarkMode', JSON.stringify(newMode));
    }
  };

  // Don't apply dark class globally - let individual pages handle their own theme
  // The home page will handle its own dark theme application

  return (
    <ThemeContext.Provider value={{ isDarkMode, toggleDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider; 