import * as React from 'react';
import { Box, CircularProgress } from '@mui/material';


// Skeleton Loader with Material-UI

const Loading2 = () => (
  <div className="p-6 space-y-6 animate-pulse bg-gray-50 min-h-screen">

    <div className="flex justify-between items-center">
      {/* Title Placeholder */}
      <div className="h-6 bg-gray-300 rounded w-3/5"></div>

      {/* Button and Three-Dot Icon */}
      <div className="flex items-center space-x-4">
        {/* Orange Button Placeholder */}
        <div className="h-10 bg-primary-300 rounded w-36"></div>
        {/* Three-Dot Icon Placeholder */}
        <div className="h-6 bg-gray-300 rounded-full w-6"></div>
      </div>
    </div>

    {/* Description Section */}
    <div className="space-y-4">
      {/* Section Title */}
      <div className="h-5 bg-gray-300 rounded w-1/6"></div>
      {/* Text Placeholder Lines */}
      <div className="h-4 bg-gray-200 rounded w-full"></div>
      <div className="h-4 bg-gray-200 rounded w-11/12"></div>
      <div className="h-4 bg-gray-200 rounded w-10/12"></div>
    </div>

    {/* Functional Requirements Section */}
    <div className="space-y-4">
      {/* Section Title */}
      <div className="h-5 bg-gray-300 rounded w-1/4"></div>
      {/* Text Placeholder Lines */}
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-11/12"></div>
        <div className="h-4 bg-gray-200 rounded w-10/12"></div>
      </div>
    </div>

    {/* Architectural Requirements Section */}
    <div className="space-y-4">
      {/* Section Title */}
      <div className="h-5 bg-gray-300 rounded w-1/3"></div>
      {/* Text Placeholder Lines */}
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-11/12"></div>
        <div className="h-4 bg-gray-200 rounded w-10/12"></div>
      </div>
    </div>
  </div>
);



// Pulse Skeleton Loader
const LoadingSkeleton = () => (
  <div className="text-center p-4">
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
      <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
      <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
    </div>
  </div>
);

// Circular Loader with Material-UI
const CircularLoader = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
    <CircularProgress />
  </Box>
);

export { Loading2, LoadingSkeleton, CircularLoader };
