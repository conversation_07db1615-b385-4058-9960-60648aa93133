import React,{useContext} from "react";
import { Check } from "lucide-react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

// Add this inside the PersonalRepositorySection component
interface ProductCardProps {
  planType: string;
  productName: string;
  productDescription: string;
  credits: string;
  prices?: string[];
  selectedPrice?: string;
  onPriceSelect?: (price: string) => void;
  features: string[];
  icon?: React.ReactNode;
  onClick: () => void;
  isLoggedIn: boolean;
  currentPlan?: boolean;
  dynamicCredits: string;
  currentPlanTokens?: string; // Add this prop to track current plan's tokens
  currentProductName?: string;
  Restrict: number
}

const NewProductCard = ({
  planType,
  productName,
  productDescription,
  credits,
  prices,
  selectedPrice,
  onPriceSelect,
  features,
  icon,
  onClick,
  isLoggedIn,
  currentPlan,
  dynamicCredits,
  currentPlanTokens = "0", // Default to 0 if not provided
  currentProductName,
  Restrict
}: ProductCardProps) => {
  let isHighlighted = currentProductName?.toLowerCase().includes(productName.toLowerCase());

  const isTeams = planType.toLowerCase().includes('teams');
  const displayCredits = dynamicCredits || credits;

  const {showAlert} = useContext(AlertContext)
  // Convert tokens to numbers for comparison by removing commas and converting to integer
  const planTokens = parseInt(dynamicCredits);
  const userCurrentTokens = parseInt(currentPlanTokens.replace(/,/g, ''));
  const isLowerTierPlan = planTokens < userCurrentTokens;
  
   const handleUpgradeClick = () => {
    // Check if this plan requires price selection and no price is selected
    if (prices && prices.length > 0 && !selectedPrice) {
      // You can either show an alert or pass an error callback
      showAlert('Please select a price first','info');
      return;
    }
    
    // If validation passes, proceed with the original onClick
    onClick();
  };

  return (
    <div className={`relative rounded-lg h-full border ${isHighlighted ? 'border-primary-400/20 bg-gradient-to-tr from-[#1C1C1C] from-70% to-primary-400/20' : 'border-gray-600/20 bg-[#1C1C1C]'}`}>
      <div className={"flex flex-col p-6 h-full text-white"}>
        {/* Product Name and Description */}
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="font-inter typography-heading-4 font-weight-medium mb-2">
              {productName}
              {currentPlan && (<span className="mb-1 ml-3bg-yellow-100 text-yellow-800 typography-body-sm font-weight-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-yellow-900 dark:text-yellow-300">{currentPlan}</span>)}
            </h3>
            <p className="font-inter typography-body-sm font-weight-light min-h-12 max-w-60 text-wrap text-gray-400">
              {productDescription}
            </p>
          </div>
          {icon && (
            <div className={`p-2 rounded-lg ${isHighlighted ? 'bg-primary-400/20' : 'bg-gray-600/20'
              }`}>
              {icon}
            </div>
          )}
        </div>

        {/* Tokens Display */}
        <div className="mb-6">
          <div className="flex items-baseline gap-2">
            <span className="typography-heading-2 font-weight-semibold">{Number(displayCredits).toLocaleString()}</span>
            <span className="typography-body-sm text-gray-400">credits/month</span>
          </div>
        </div>

        {/* Price Selection */}
        {/* Price Selection */}
        {prices && prices.length > 0 ? (
          <div className="flex flex-col gap-2 mb-6 min-h-[90px]">
            <div className="flex items-center flex-1 w-full justify-evenly gap-2 p-1.5 rounded-lg border border-semantic-gray-600/30">
              {prices.map((price) => (
                <button
                  key={price}
                  onClick={() => onPriceSelect?.(price)}
                  className={`px-4 w-full py-2 rounded-lg typography-body-sm transition-all duration-200 border-semantic-gray-600/30 ${selectedPrice === price
                      ? `${isHighlighted ? 'bg-primary/20' : 'bg-custom-bg-primary/10'}
                 border border-semantic-gray-500/30`
                      : 'hover:bg-semantic-gray-700/50 '
                    }`}
                >
                  ${price}
                </button>
              ))}
            </div>
            <span className="typography-body-sm font-weight-light text-center text-gray-400">USD/month</span>
          </div>
        ) : (
          <div className="mb-6 min-h-[90px] flex flex-col justify-center">
            <div className="flex items-baseline justify-center gap-3">
              <span className="typography-heading-1 font-weight-semibold">Forever</span>
              {/* <span className="typography-body-sm text-gray-400">forever</span> */}
            </div>
          </div>
        )}
        {/* Action Button */}
        {/* Action Button */}
        <button
          className={`w-full px-4 py-3 rounded-lg transition-colors font-weight-medium ${planTokens <= Restrict || planType === "free"
              ? 'bg-white/10 cursor-not-allowed'
              : planTokens > Restrict
                ? 'bg-orange-500 hover:bg-orange-600'
                : 'bg-white/20 hover:bg-white/30'
            }`}
          onClick={handleUpgradeClick}
          disabled={planTokens <= Restrict || planType === "free"}
        >
          {planTokens === Restrict
            ? "Current Plan"
            : planTokens < Restrict
              ? "Plan Unavailable"
              : planType === "free"
                ? "Current Plan"
                : isLoggedIn
                  ? `Upgrade to ${productName}${currentPlan ? ` ${currentPlan}` : ''}`
                  : `Choose ${productName}`}
        </button>

        {/* Features List */}
        <div className="space-y-3 mt-8">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start gap-2">
              <Check className={`w-4 h-4 mt-1 ${isHighlighted ? 'text-orange-400' : 'text-gray-400'
                }`} />
              <span className="typography-body-sm text-gray-300">{feature}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NewProductCard;