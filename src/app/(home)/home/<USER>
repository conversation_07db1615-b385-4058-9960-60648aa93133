"use client";
import React from "react";
import dynamic from "next/dynamic";
import ClientSideComponent from "@/components/Home/ClientSideComponent";
import "../../../app/loaders.css";

const Header = dynamic(() => import("@/components/Home/Header"), {
  ssr: false,
  loading: () => <div className="loader"><div className="spinner"></div></div>
});



export default function Page() {
  return (
    <div className="flex flex-col justify-start items-center flex-grow h-screen">
      <div className="tab-effect project-panel-content">
        <span>Get Started</span>
      </div>
      <Header className="max-h-[40vh] w-full" />
      <div className="flex flex-col items-center flex-grow w-full mt-4 max-h-[60vh] overflow-auto custom-scrollbar px-10">
        <div className="w-3/4">
          <ClientSideComponent />
        </div>
      </div>
    </div>
  );
}
